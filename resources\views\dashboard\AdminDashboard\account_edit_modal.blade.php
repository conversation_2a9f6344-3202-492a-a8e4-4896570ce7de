<div class="modal fade create-filter edit-course-modal" id="edit-account-modal" tabindex="-1" role="dialog" aria-labelledby="editModalTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalTitle">Edit Account</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editAccountForm" action="{{ url('accounts/'.$account->AcctId) }}" method="POST">
                    @csrf
                    @method('PUT')  <!-- Use PUT for updating an existing resource -->

                    <div class="form-group">
                        <label for="accountNameEdit">Account Name<span class="required-star">*</span></label>
                        <input type="text" class="form-control" id="accountNameEdit" name="acount_name" value="{{ $account->AcctNm }}" placeholder="Enter account name" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location<span class="required-star">*</span></label>
                        <input type="text" class="form-control locationInput" id="locationEdit" name="location" placeholder="Enter location" required value="{{ $account->location ?? '' }}">
                        <input type="hidden" name="latitude" class="latitude" value="">
                        <input type="hidden" name="longitude" class="longitude" value="">
                        <input type="hidden" name="city" class="city" value="">
                        <input type="hidden" name="state" class="state" value="">
                        <input type="hidden" name="zip_code" class="zip" value="">
                    </div>

                    <div class="form-group">
                        <label for="countryEdit">Country<span class="required-star">*</span></label>
                        <select class="form-control country" id="countryEdit" name="country" required>
                            <option value="" disabled>Select a country</option>
                            @foreach($countries as $country)
                                <option value="{{ $country->CntryCd }}"
                                        @if($country->CntryNm == $account->country->CntryNm) selected @endif>
                                    {{ $country->CntryNm }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone<span class="required-star">*</span></label>
                        <input type="text" class="form-control contact_number" name="phone" placeholder="(+123) 4568 7890 8520" required value="{{ $account->phoneNum ?? ''}}">
                        <div class="alert-info"></div>
                    </div>
                    <div class="form-group">
                        <label for="representativeEdit">Account Managers<span class="required-star">*</span></label>
                        <select class="form-control" id="representativeEdit" name="acctMgrId" required>
                            <option value="" selected disabled>Select Account Managers</option>
                            @foreach($accountManagers as $accountManager)
                                <option value="{{$accountManager->AcctMgrId ?? ''}}" @if(isset($account->AcctMgrId) && $accountManager->AcctMgrId == $account->AcctMgrId) selected @endif>{{$accountManager->AcctMgrNm??''}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="salesTypeEdit">Sales Type<span class="required-star">*</span></label>
                        <select class="form-control" id="salesTypeEdit" name="acctType" required>
                            <option value="">Select Sales Type</option>
                            @foreach($acctTypes as $acctType)
                                <option value="{{ $acctType->AcctTypeId }}" @if(isset($account->AcctTypeId) && $acctType->AcctTypeId == $account->type->AcctTypeId) selected @endif>
                                    {{ $acctType->AcctTypeDsc }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="statusEdit">Status<span class="required-star">*</span></label>
                        <select class="form-control" id="statusEdit" name="status" required>
                            <option value="" selected disabled>Select Status</option>
                            @foreach($allStatus as $status)
                                <option value="{{ $status->AcctStatId }}" @if($status->AcctStatId == $account->status->AcctStatId) selected @endif>
                                    {{ $status->AcctStatDsc }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="websiteEdit">Website Address<span class="required-star">*</span></label>
                        <input type="url" class="form-control" id="websiteEdit" name="website" value="{{ $account->AcctURL }}" placeholder="Enter website URL" required>
                    </div>

                    <div class="form-group">
                        <label for="shippingOptionEdit">Default Shipping Option<span class="required-star">*</span></label>
                        <select class="form-control" id="shippingOptionEdit" name="shipVia" required>
                            <option value="" selected disabled>Select Shipping Option</option>
                            @foreach($shipVias as $shipVia)
                                <option value="{{ $shipVia->ShipViaId }}" @if(isset($account->ShipViaId) && $shipVia->ShipViaId == $account->shipVia->ShipViaId) selected @endif>
                                    {{ $shipVia->ShipViaDsc }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="notesEdit">Notes / Comments</label>
                        <textarea class="form-control" id="notesEdit" name="notes" rows="3" placeholder="Enter any notes or comments">{{ $account->Comm }}</textarea>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="submit" class="btn light_green_btn" form="editAccountForm">Save</button>
                <button type="button" class="btn cancel_btn" data-bs-dismiss="modal">Close</button>
            </div>

        </div>
    </div>
</div>
