<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="keywords" content="">
    <meta name="author" content="">
    <meta name="description" content="{{ App\Models\Setting::first()->description??'' }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{asset('')}}{{ App\Models\Setting::first()->favicon??'' }}">
    <title>{{ App\Models\Setting::first()->title??'' }}</title>

    <link rel="stylesheet" href="https://cdn.datatables.net/2.2.2/css/dataTables.dataTables.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
          integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
          crossorigin="anonymous" referrerpolicy="no-referrer"/>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">
    <link rel="stylesheet" href="{{ asset('website/assets/css/dashboard.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css"/>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.10.1/dropzone.min.css" rel="stylesheet"/>
    @stack('css')
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

</head>
<body>
<div class="main_wrapper" id="wrapper">
    <div class="preloader">
        <div class="cssload-speeding-wheel">
            <img class="img-fluid" src="{{ asset('website') }}/assets/images/footer_logo.svg" class="uploadIcon">
        </div>
    </div>
    @include('theme.layout.partials.sidebar')
    <div class="page-wrapper">
        @include('theme.layout.partials.navbar')
        @yield('content')
    </div>
    <footer class="footer ">
        <div class="footer_box">
            <center> {{ App\Models\Setting::first()->footer_text??'' }} </center>
            <p>Privacy Policy | Terms & Conditions</p>
        </div>
    </footer>
</div>
<script src="//cdn.datatables.net/2.2.2/js/dataTables.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{{--<script src="https://maps.googleapis.com/maps/api/js?key={{env('GOOGLE_MAP_API_KEY')}}&libraries=places&callback=initAutocomplete" async defer></script>--}}

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.3/dist/jquery.validate.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"
        referrerpolicy="no-referrer"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
<script
    src="https://maps.googleapis.com/maps/api/js?key={{env('GOOGLE_MAP_API_KEY')}}&libraries=places&callback=initAutocomplete"
    async defer></script>

<script>
    $('.go_back').click(function () {
        window.history.back();
    });

    $(document).ready(function () {
        initializePhoneInput();

        var dataTable = $(".datatable").DataTable({
            "lengthChange": false,
            "info": false,
            "ordering": true,
            "paging": true,
        });


        $(document).on("input", '.searchinput', function () {
            var searchValue = $(this).val();
            dataTable.search(searchValue).draw(); // Use the correct instance
        });

        $(document).on('click', '.dropdown-toggle', function () {
            $('table .dropdown-menu').not($(this).next('table .dropdown-menu')).collapse('hide');
        });

        $(window).on('load', function () {
            $(".app_sidebar ul.sidebar_menus li.sidebar_dropdown.active .collapse").collapse('show');

            $('.custom_dropdown_menu').click(function () {
                $('.custom_dropdown_menu').removeClass('active');
                $(this).addClass('active');
                updateActiveIndicator();
            });

            $('.cruds_collapse ul.collapse').on('shown.bs.collapse', function () {
                var dynamicHeight = 45;
                $('.cruds_collapse ul.collapse li.menu_content').each(function () {
                    dynamicHeight += $(this).outerHeight(true);
                });

                $('.cruds_stock .custom_dropdown_menu').css('--dynamic-height', dynamicHeight + 'px');

                // Call updateActiveIndicator after the collapse event is triggered
                updateActiveIndicator();
            });

            function updateActiveIndicator() {
                const $activeLink = $(".custom_dropdown_menu.active");
                if ($activeLink.length) {
                    const $li = $activeLink.closest('li');
                    const $parentList = $li.closest('ul');
                    let totalHeight = 0;
                    const $allItems = $parentList.find('> li');
                    const currentIndex = $allItems.index($li);
                    for (let i = 0; i < currentIndex; i++) {
                        totalHeight += $($allItems[i]).outerHeight(true);
                    }
                    totalHeight += $li.outerHeight() / 2;
                    $activeLink.css('--dynamic-height', totalHeight + 'px');
                }
                // Ensure active dropdowns stay visible
                $(".app_sidebar ul.sidebar_menus li.sidebar_dropdown.active .collapse").collapse('show');

                if ($('.sidebar_dropdown  .collapse  ul.collapse').hasClass('show')) {
                    var dynamicHeight = 45;
                    $('.cruds_collapse ul.collapse li.menu_content').each(function () {
                        dynamicHeight += $(this).outerHeight(true);
                    });

                    $('.cruds_stock .custom_dropdown_menu').css('--dynamic-height', dynamicHeight + 'px');

                }


            }

            updateActiveIndicator();

            $(window).resize(function () {
                updateActiveIndicator();
            });

            $('.cruds_collapse ul.collapse ').on('shown.bs.collapse', function () {
                var dynamicHeight = 45;
                $('.cruds_collapse ul.collapse li.menu_content').each(function () {
                    dynamicHeight += $(this).outerHeight(true);
                });

                $('.cruds_stock .custom_dropdown_menu').css('--dynamic-height', dynamicHeight + 'px');
            });

            $('.cruds_collapse ul.collapse').on('hidden.bs.collapse', function () {
                $('.cruds_stock .custom_dropdown_menu').css('--dynamic-height', '50px');
            });
        });

        $(".app_sidebar ul.sidebar_menus li.sidebar_dropdown.active .collapse").collapse('show');

        // Dropdown hide logic
        $(document).on('hide.bs.dropdown', function (e) {
            if (
                $(e.clickEvent?.target).closest('.cancel_btn').length ||
                $(e.clickEvent?.target).closest('.btn_close').length
            ) {
                return true;
            }
            e.preventDefault();
            return false;
        });
        $('.modal').modal({
            backdrop: 'static',
            keyboard: false
        });
        // Close navbar dropdowns when clicking outside
        $(document).on('click', function (e) {
            if (!$(e.target).closest('.custom_navbar .dropdown').length) {
                $('.custom_navbar .dropdown-menu').removeClass('show');
                $('.custom_navbar .dropdown-toggle').removeClass('show');
            }
        });

        $(document).on('click', function (e) {
            if (!$(e.target).closest('.table-responsive .table .dropdown').length) {
                $('.table-responsive .table .dropdown-menu').removeClass('show');
                $('.table-responsive .table .dropdown-toggle').removeClass('show');
            }
        });
        $(document).on('click', function (e) {
            if (!$(e.target).closest('.inner_section_dragable .dropdown').length) {
                $('.inner_section_dragable .dropdown-menu').removeClass('show');
                $('.inner_section_dragable .dropdown-toggle').removeClass('show');
            }
        });


    });
    // Hide the preloader once the page is fully loaded
    window.onload = function () {
        document.querySelector('.preloader').style.display = 'none';
    }

    function initializePhoneInput() {
        document.querySelectorAll('.contact_number').forEach(function (phoneInputField) {
            const phoneInput = window.intlTelInput(phoneInputField, {
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
                nationalMode: true,
                formatOnDisplay: true,
                autoPlaceholder: "polite",
                separateDialCode: false,
            });

            const phoneNumber = phoneInputField.value;
            if (phoneNumber) {
                phoneInput.setNumber(phoneNumber);
            }

            const info = phoneInputField.closest('.form-group').querySelector('.alert-info');

            phoneInputField.addEventListener("input", function (event) {
                const isValid = phoneInput.isValidNumber();
                const phoneNumber = phoneInput.getNumber();

                if (isValid) {
                    info.style.display = "block";
                    info.innerHTML = `✅ Valid phone number: <strong>${phoneNumber}</strong>`;
                } else {
                    info.style.display = "block";
                    info.innerHTML = `❌ Invalid phone number. Please enter a valid one.`;
                }
            });

            phoneInputField.addEventListener("keypress", function (e) {
                const char = String.fromCharCode(e.which);
                if (!/[0-9+\-\s]/.test(char)) {
                    e.preventDefault();
                }
            });

            phoneInputField.addEventListener("blur", function () {
                if (phoneInput.isValidNumber()) {
                    phoneInputField.classList.add("valid");
                    phoneInputField.classList.remove("invalid");
                } else {
                    phoneInputField.classList.add("invalid");
                    phoneInputField.classList.remove("valid");
                }
            });
        });
    }

    function initAutocomplete() {
        document.querySelectorAll('.locationInput').forEach(function (inputField) {
            var autocomplete = new google.maps.places.Autocomplete(inputField, {
                componentRestrictions: { country: ['nz', 'au', 'us'] }
            });

            autocomplete.addListener('place_changed', function () {
                var place = autocomplete.getPlace();
                if (!place.geometry) return;

                var lat = place.geometry.location.lat();
                var lng = place.geometry.location.lng();

                // Use the closest form if this input is inside a form
                var formElement = inputField.closest('form') || document;

                const setField = (selector, value) => {
                    var field = formElement.querySelector(selector);
                    if (field) {
                        if (field.tagName.toLowerCase() === 'select') {
                            field.value = value || '';
                            field.dispatchEvent(new Event('change')); // Optional: trigger if needed
                        } else {
                            field.value = value || '';
                        }
                    }
                };

                // Set lat/lng
                setField('.latitude', lat);
                setField('.longitude', lng);

                // Parse and set address components
                var city, state, zip, country;
                place.address_components.forEach(function (component) {
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (component.types.includes('postal_code')) {
                        zip = component.long_name;
                    }
                    if (component.types.includes('country')) {
                        country = component.short_name;
                    }
                });

                setField('.city', city);
                setField('.state', state);
                setField('.zip', zip); // use .zip instead of .zip_code since your class is .zip
                setField('.country', country); // this will set the <select>
            });
        });
    }
</script>
<script>
    function toggleShaftMenu() {
        const shaftMenu = document.getElementById('ShaftSubmenu');
        const shaftLink = document.querySelector('a[href="{{ url('shafts') }}"]');

        // Check if the current page is 'shafts' and toggle the menu
        if (shaftMenu && shaftLink) {
            if (shaftMenu.classList.contains('show')) {
                shaftMenu.classList.remove('show');
            } else {
                shaftMenu.classList.add('show');
            }
        }

        // Close any open dropdown menus when navigating to a different page
        if (!window.location.pathname.includes('shafts')) {
            shaftMenu.classList.remove('show');
        }
    }

    // Ensure the dropdown is closed when page is loaded (for direct navigation)
    document.addEventListener('DOMContentLoaded', function () {
        const shaftMenu = document.getElementById('ShaftSubmenu');
        if (shaftMenu && !window.location.pathname.includes('shafts')) {
            shaftMenu.classList.remove('show');
        }
    });

    // Close other menus when a new page is loaded
    window.addEventListener('popstate', function () {
        const shaftMenu = document.getElementById('ShaftSubmenu');
        if (!window.location.pathname.includes('shafts')) {
            shaftMenu.classList.remove('show');
        }
    });
</script>


<script>
    @if(session()->has('message'))
    Swal.fire({
        title: "{{session()->get('title')??'success!'}}",
        html: "{{@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))}}",
        icon: "{{session()->get('type')??'success'}}",
        timer: 5000,
        buttons: false,
    });
    @endif
    @if(session()->has('flash_message'))
    Swal.fire({
        title: "{{@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))}}",
        icon: "{{  session()->get('type')??'success'}}",
        timer: 5000,
        buttons: false,
    });
    @endif
    function showDeleteConfirmation(button) {
        Swal.fire({
            title: 'Confirm Delete',
            text: 'Are you sure you want to delete this item?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Delete',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                button.closest('.delete-form').submit();
            }
        });
    }
</script>
@stack('js')
</body>
</html>
